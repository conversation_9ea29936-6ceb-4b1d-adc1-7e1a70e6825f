// GSAP Hero Section Animations
document.addEventListener('DOMContentLoaded', function() {
    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger);

    // Rotating Text Animation
    function initRotatingText() {
        const words = document.querySelectorAll('.rotate-word');
        let currentIndex = 0;

        function rotateWords() {
            // Remove active class from current word
            words[currentIndex].classList.remove('active');
            words[currentIndex].classList.add('exit');

            // Move to next word
            currentIndex = (currentIndex + 1) % words.length;

            // Add active class to next word after a delay
            setTimeout(() => {
                words.forEach(word => word.classList.remove('exit'));
                words[currentIndex].classList.add('active');
            }, 300);
        }

        // Start rotation after initial load
        setTimeout(() => {
            setInterval(rotateWords, 2500);
        }, 2000);
    }

    // Initialize rotating text
    initRotatingText();

    // Create timeline for hero animations
    const heroTl = gsap.timeline();

    // Animate hero badge first
    heroTl.to('.hero-badge', {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: "power2.out"
    })

    // Animate hero title lines
    .to('.hero-title-line', {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: "power3.out"
    }, "-=0.3")
    
    // Animate title highlight underline
    .to('.hero-title-highlight::after', {
        width: '100%',
        duration: 0.8,
        ease: "power2.out"
    }, "-=0.5")
    
    // Animate description container
    .to('.hero-description', {
        y: 0,
        opacity: 1,
        duration: 0.8,
        ease: "power2.out"
    }, "-=0.8")

    // Animate description lines
    .to('.hero-desc-line', {
        x: 0,
        opacity: 1,
        duration: 0.6,
        stagger: 0.2,
        ease: "power2.out"
    }, "-=0.4")
    
    // Animate buttons
    .to('.hero-btn', {
        y: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.7)"
    }, "-=0.6")
    
    // Animate stats
    .to('.hero-stat', {
        y: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.15,
        ease: "power2.out"
    }, "-=0.4")
    
    // Animate floating cards
    .to('.floating-card', {
        scale: 1,
        opacity: 1,
        duration: 1,
        stagger: 0.3,
        ease: "back.out(1.7)"
    }, "-=1");

    // Counter animation for stats
    function animateCounter(element) {
        const target = parseInt(element.dataset.count);
        const duration = 2;
        
        gsap.to(element, {
            innerHTML: target,
            duration: duration,
            ease: "power2.out",
            snap: { innerHTML: 1 },
            onUpdate: function() {
                element.innerHTML = Math.ceil(element.innerHTML);
            }
        });
    }

    // Trigger counter animations when stats come into view
    ScrollTrigger.create({
        trigger: '.hero-stats',
        start: 'top 80%',
        onEnter: () => {
            document.querySelectorAll('.hero-stat-number').forEach(animateCounter);
        }
    });

    // Floating cards continuous animation
    gsap.to('.floating-card-1', {
        y: -20,
        rotation: 5,
        duration: 3,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut"
    });

    gsap.to('.floating-card-2', {
        y: -15,
        rotation: -3,
        duration: 4,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut",
        delay: 1
    });

    gsap.to('.floating-card-3', {
        y: -25,
        rotation: 8,
        duration: 5,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut",
        delay: 2
    });

    gsap.to('.floating-card-4', {
        y: -18,
        rotation: -6,
        duration: 3.5,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut",
        delay: 2.5
    });

    // Background circles animation
    gsap.to('.bg-circle-1', {
        rotation: 360,
        duration: 20,
        repeat: -1,
        ease: "none"
    });

    gsap.to('.bg-circle-2', {
        rotation: -360,
        duration: 25,
        repeat: -1,
        ease: "none"
    });

    gsap.to('.bg-circle-3', {
        rotation: 360,
        duration: 30,
        repeat: -1,
        ease: "none"
    });

    // Button hover animations
    document.querySelectorAll('.hero-btn').forEach(btn => {
        btn.addEventListener('mouseenter', () => {
            gsap.to(btn, {
                scale: 1.05,
                duration: 0.3,
                ease: "power2.out"
            });
            
            gsap.to(btn.querySelector('i'), {
                x: 5,
                duration: 0.3,
                ease: "power2.out"
            });
        });

        btn.addEventListener('mouseleave', () => {
            gsap.to(btn, {
                scale: 1,
                duration: 0.3,
                ease: "power2.out"
            });
            
            gsap.to(btn.querySelector('i'), {
                x: 0,
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });

    // Parallax effect for background elements
    gsap.to('.bg-circle-1', {
        y: -50,
        scrollTrigger: {
            trigger: '.hero-section',
            start: 'top bottom',
            end: 'bottom top',
            scrub: 1
        }
    });

    gsap.to('.bg-circle-2', {
        y: -30,
        scrollTrigger: {
            trigger: '.hero-section',
            start: 'top bottom',
            end: 'bottom top',
            scrub: 1.5
        }
    });

    gsap.to('.bg-circle-3', {
        y: -40,
        scrollTrigger: {
            trigger: '.hero-section',
            start: 'top bottom',
            end: 'bottom top',
            scrub: 2
        }
    });

    // Wave image parallax
    gsap.to('.wave-image', {
        y: 20,
        scrollTrigger: {
            trigger: '.hero-section',
            start: 'top bottom',
            end: 'bottom top',
            scrub: 0.5
        }
    });

    // Floating cards parallax
    gsap.to('.floating-card', {
        y: -30,
        scrollTrigger: {
            trigger: '.hero-section',
            start: 'top bottom',
            end: 'bottom top',
            scrub: 1
        }
    });

    // Add magnetic effect to buttons
    document.querySelectorAll('.hero-btn').forEach(btn => {
        btn.addEventListener('mousemove', (e) => {
            const rect = btn.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;
            
            gsap.to(btn, {
                x: x * 0.1,
                y: y * 0.1,
                duration: 0.3,
                ease: "power2.out"
            });
        });

        btn.addEventListener('mouseleave', () => {
            gsap.to(btn, {
                x: 0,
                y: 0,
                duration: 0.5,
                ease: "elastic.out(1, 0.3)"
            });
        });
    });

    // Refresh ScrollTrigger on window resize
    window.addEventListener('resize', () => {
        ScrollTrigger.refresh();
    });
});
